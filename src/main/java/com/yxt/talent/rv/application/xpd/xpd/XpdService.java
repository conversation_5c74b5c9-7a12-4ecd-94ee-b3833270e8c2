package com.yxt.talent.rv.application.xpd.xpd;

import cn.hutool.core.lang.Pair;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.base.bean.arrange.ActivityArrangeItem4ActvMgr;
import com.yxt.aom.base.bean.arrange.ActivityArrangeTree;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.manager.arrange.ArrangeManager;
import com.yxt.aom.base.manager.control.MemberStatisticsManager;
import com.yxt.aom.base.service.arrange.ArrangeService;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.repo.RedisRepository;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireBaseInfo;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.talent.rv.infrastructure.service.remote.MessageAclService;
import com.yxt.talent.rv.infrastructure.service.remote.dto.MessageDTO;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class XpdService {
    private final ArrangeService arrangeService;
    private final AuthService authService;
    private final MessageAclService messageAclService;
    private final ActivityService activityService;
    private final ActivityParticipationService activityParticipationService;
    private final SptalentsdFacade spTalentSdFacade;
    private final XpdMapper xpdMapper;
    private final MemberStatisticsManager memberStatisticsManager;
    private final ArrangeManager arrangeManager;
    private static final String ENDFAIL_NOTIFY = "sptalentrv_endfail_notify";
    private final RedisRepository talentRedisRepository;
    private final CoreAclService coreAclService;
    private static final String PROJECT_URL = "/amapp/d1a5b2b1/sprv/#/project-console?id=";

    /**
     * 获取项目下的所有活动， 包括统计数据，还包含了用户数据权限的。如果要获取项目下所有活动列表，不适合该方法
     * @param prjId
     * @param regId
     * @return
     */
    public List<ActivityArrangeItem4ActvMgr> getActivityList(String prjId, String regId){
        ActivityArrangeTree<ActivityArrangeItem4ActvMgr>
            activityBean = arrangeService.listArrangeTree4ActvMgr(authService.getUserCacheDetail(), prjId, regId, false);
        if (activityBean != null && CollectionUtils.isNotEmpty(activityBean.getDatas())){
            List<ActivityArrangeItem4ActvMgr> list = getAllActvs(activityBean.getDatas());
            if (CollectionUtils.isNotEmpty(list)){
                return list.stream().filter(item -> item.getItemType() == 0).collect(Collectors.toList());
            }
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }


    /**
     * 获取项目下的所有绩效活动
     * @param xpdId
     * @return
     */
    public List<ActivityArrangeItem> getPerfActvsByXpdId(String orgId, String xpdId){
        Activity activity = findAomPrjByAomId(orgId, xpdId);
        if (activity == null){
            throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
        }

        List<ActivityArrangeItem> list = arrangeManager.listArrangeItem(orgId, activity.getId(), 0);
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list.stream().filter(item -> item.getRefRegId().equals(UacdTypeEnum.ACTV_PERF.getRegId())).collect(Collectors.toList());
    }

    public List<ActivityArrangeItem4ActvMgr> getAllActvs(List<ActivityArrangeItem4ActvMgr> datasList) {
        List<ActivityArrangeItem4ActvMgr> result = new ArrayList<>();
        for (ActivityArrangeItem4ActvMgr data : datasList) {
            result.add(data);
            if (data.getChildren() != null && !data.getChildren().isEmpty()) {
                result.addAll(getAllActvs(data.getChildren()));
            }
        }
        return result;
    }

    public boolean finishProject(String orgId,String opUserId, String aomActvId){
        Activity activity = this.activityService.findById(orgId, aomActvId);
        Long partId = this.activityParticipationService.getParticipationId(orgId, aomActvId);

        List<IdName> list = this.activityService.listActivityMgr(orgId, aomActvId, partId);

//        List<ActivityArrangeItem4ActvMgr> actvMgrs = getActivityList(aomActvId, activity.getActvRegId());
//        if (CollectionUtils.isEmpty(actvMgrs)){
//            log.info("未发现活动，{}",aomActvId);
//            return false;
//        }
        log.info("项目负责人，{}, partId:{}, aomActvId:{}", BeanHelper.bean2Json(list, JsonInclude.Include.NON_NULL), partId, aomActvId);
        if (!memberStatisticsManager.isCompleted(orgId, aomActvId, UacdTypeEnum.PRJ_XPD.getRegId(), null)){
            log.info("存在未完成的人员，{}", aomActvId);

            String redisKey = String.format(RedisKeys.CACHE_SEND_MESSAGE,orgId,aomActvId);
            if (StringUtils.isNotBlank(talentRedisRepository.getValueByKey(redisKey))){
                log.info("发送消息，24小时发送一次，{}", aomActvId);
                return false;
            }

            if (CollectionUtils.isNotEmpty(list)){
                List<String> mgrUserIds = list.stream().map(IdName::getId).collect(Collectors.toList());
                MessageDTO messageDTO = new MessageDTO();
                messageDTO.setTemplateCode(ENDFAIL_NOTIFY);
                messageDTO.setOrgId(orgId);
                messageDTO.setUserId(opUserId);
                messageDTO.setUserIds(mgrUserIds);
                HashMap<String, String> placeholderMap = new HashMap<>(8);
                placeholderMap.put("{{projectName}}", activity.getActvName());
                messageDTO.setPlaceholderMap(placeholderMap);

                String url = coreAclService.getScanentryURL(orgId,
                    PROJECT_URL+activity.getId(), "");
                placeholderMap.put("{{url}}", url);

                // 发送消息
                log.info("自动结束发送消息，{}", BeanHelper.bean2Json(messageDTO, JsonInclude.Include.NON_NULL));
                messageAclService.sendTemplateMessage(messageDTO);

                // 标记此项目已经发送过消息，24小时只发一次。
                talentRedisRepository.setValue(redisKey, "1");
                talentRedisRepository.expire(redisKey, 24, TimeUnit.HOURS);

                return false;
            }
        }

        return true;
    }

    /**
     * 校验模型ID，模型内的维度必须大于等于2
     * @param modelId
     */
    public void validateModelId(String orgId, String modelId) {
        ModelInfo modelInfo = getModel(orgId, modelId);
        List<ModelRequireBaseInfo> dimList = modelInfo.getDms();
        if (CollectionUtils.isEmpty(dimList)){
            throw new ApiException(ExceptionKeys.XPD_MODEL_DIM_NOT_EXIST);
        }
        Validate.isTrue(checkChildDimensions(dimList), ExceptionKeys.XPD_MODEL_DIM_SIZE);
    }

    public ModelInfo getModel(String orgId, String modelId) {
        ModelInfo modelInfo = spTalentSdFacade.getModelInfo(orgId, modelId);
        if (modelInfo == null){
            throw new ApiException(ExceptionKeys.XPD_MODEL_NOT_EXIST);
        }
        return modelInfo;
    }


    private boolean checkChildDimensions(List<ModelRequireBaseInfo> dimList) {
        if (CollectionUtils.isEmpty(dimList)) {
            return false;
        }

        if (dimList.size() >= 2) {
            return true; // 当前级的size大于等于2，满足条件
        }

        for (ModelRequireBaseInfo baseInfo : dimList) {
            List<ModelRequireBaseInfo> childs = baseInfo.getChilds();
            if (checkChildDimensions(childs)) {
                return true; // 递归检查子维度，如果满足条件则返回true
            }
        }
        return false; // 所有子维度都不满足条件
    }

    /**
     * 通过活动ID找到项目的参与者ID
     * @param orgId
     * @param actvId 活动ID
     * @return
     */
    public Long getPartIdByActvId(String orgId, String actvId) {
        Activity aomActivity = activityService.findById(orgId, actvId);
        if (aomActivity == null){
            throw new ApiException(ExceptionKeys.ACTV_NOT_EXIST);
        }
        Long partId = activityParticipationService.getParticipationId(orgId, aomActivity.getSourceId());
        if (Objects.isNull(partId)) {
            throw new ApiException(ExceptionKeys.PARTICIPATION_NOT_EXIST);
        }
        return partId;
    }

    /**
     * 通过活动ID找到项目的参与者ID
     * @param orgId
     * @param actvId 活动ID
     * @return
     */
    public Pair<String, Long> getPartAndPrjId(String orgId, String actvId) {
        Activity aomActivity = activityService.findById(orgId, actvId);
        if (aomActivity == null){
            throw new ApiException(ExceptionKeys.ACTV_NOT_EXIST);
        }
        Long partId = activityParticipationService.getParticipationId(orgId, aomActivity.getSourceId());
        if (Objects.isNull(partId)) {
            throw new ApiException(ExceptionKeys.PARTICIPATION_NOT_EXIST);
        }
        return Pair.of(aomActivity.getSourceId(), partId);
    }


    public XpdPO findXpdByAomId(String orgId, String aomId) {
       return xpdMapper.selectByAomPrjId(orgId, aomId);
    }

    @NotNull
    public Activity findAomPrjByAomId(String orgId, String xpdId) {
        XpdPO xpdPO = xpdMapper.selectById(xpdId);
        if (xpdPO == null){
            throw new ApiException(ExceptionKeys.XPD_NOT_EXIST);
        }
        return activityService.findById(orgId, xpdPO.getAomPrjId());
    }

    public List<XpdPO> findXpdsByAomIds(String orgId, List<String> aomIds) {
        if (CollectionUtils.isEmpty(aomIds)){
            return new ArrayList<>();
        }
        return xpdMapper.selectByAomIds(orgId, aomIds);
    }
}
