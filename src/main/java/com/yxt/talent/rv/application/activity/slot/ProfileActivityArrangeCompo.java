package com.yxt.talent.rv.application.activity.slot;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.activity.custom.CustomActivityArrangeCompo;
import com.yxt.aom.activity.entity.arrange.ActivityDraft;
import com.yxt.aom.activity.facade.bean.arrange.ActivityDraft4BatchCheck;
import com.yxt.aom.activity.facade.bean.arrange.Response4BatchCheck;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.BeanHelper;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.activity.ActivityProfileService;
import com.yxt.talent.rv.application.activity.dto.ActProfileCreateDTO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 动态人才评估
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Slf4j
@RequiredArgsConstructor
@Component("customActivityArrangeCompo4ActvProf")
public class ProfileActivityArrangeCompo implements CustomActivityArrangeCompo {

    private final ActivityProfileService activityProfileService;
    private final I18nComponent i18nComponent;

    @Override
    public Response4BatchCheck batchCheckDraft(ActivityDraft4BatchCheck bean) {
        log.info("ActProfile 预检入参，bean={}", BeanHelper.bean2Json(bean, JsonInclude.Include.NON_NULL));
        Map<Long, String> errorMsgMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(bean.getDatas())) {
            bean.getDatas().forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                ActProfileCreateDTO actProfileCreateDTO = BeanHelper.json2Bean(formData, ActProfileCreateDTO.class);
                if (actProfileCreateDTO == null) {
                    throw new ApiException(ExceptionKeys.ACTIVITY_FORM_DATA_ERROR);
                }
                //                activityProfileService.validateActivity(actProfileCreateDTO);
                String errorMsg = activityProfileService.validateActivity(actProfileCreateDTO);
                if (Strings.isNotBlank(errorMsg)) {
                    log.warn("LOG20373:{}", errorMsg);
                    errorMsgMap.put(activity.getItemId(), getLastMessage(i18nComponent.getI18nValue(errorMsg)));
                }
            });
        }
        //外链活动预检直接返回成功
        Response4BatchCheck result = new Response4BatchCheck();
        if (errorMsgMap.isEmpty()) {
            result.setSucceed(true);
        } else {
            result.setSucceed(false);
            result.setErrorMsgMap(errorMsgMap);
        }
        return result;
    }

    private String getLastMessage(String messageError) {
        if (StringUtils.isBlank(messageError)) {
            return messageError;
        }
        String[] splitMessage = messageError.split(";");
        return splitMessage[splitMessage.length - 1];
    }

    @Override
    public Map<Long, String> batchSaveActivity(String orgId, String actvId, String operatorId,
        List<ActivityDraft> activities2Create, List<ActivityDraft> activities2Update,
        Set<String> activityIds2Remove) {
        log.info("actProfile batchSaveActivity，activities2Create={},activities2Update={}, activityIds2Remove={}",
            BeanHelper.bean2Json(activities2Create, JsonInclude.Include.NON_NULL),
            BeanHelper.bean2Json(activities2Update, JsonInclude.Include.NON_NULL),
            BeanHelper.bean2Json(activityIds2Remove, JsonInclude.Include.NON_NULL));
        Map<Long, String> createMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(activities2Create)) {
            // 创建
            activities2Create.forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                ActProfileCreateDTO profileCreateDTO = BeanHelper.json2Bean(formData, ActProfileCreateDTO.class);
                String actId = activityProfileService.create(orgId, profileCreateDTO, operatorId);
                createMap.put(activity.getItemId(), actId);
            });
        }
        if (CollectionUtils.isNotEmpty(activities2Update)) {
            // 更新
            activities2Update.forEach(activity -> {
                // 生成UACD叶节点id
                String formData = activity.getFormData();
                ActProfileCreateDTO profileCreateDTO = BeanHelper.json2Bean(formData, ActProfileCreateDTO.class);
                profileCreateDTO.setId(activity.getRefId());
                profileCreateDTO.setAomActId(activity.getRefId());
                activityProfileService.update(orgId, profileCreateDTO, operatorId);
            });
        }

        if (CollectionUtils.isNotEmpty(activityIds2Remove)) {
            // 删除
            activityIds2Remove.forEach(id -> {
                // 生成UACD叶节点id
                activityProfileService.deleteById(orgId, id, operatorId);
            });
        }
        return createMap;
    }

    @Override
    public Map<String, String> batchUpdateFormData(String orgId, String actvId, String operatorId) {
        return Map.of();
    }

}
