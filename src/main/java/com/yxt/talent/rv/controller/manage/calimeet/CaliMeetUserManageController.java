package com.yxt.talent.rv.controller.manage.calimeet;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.aom.base.util.AomNumberUtils;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.Validate;
import com.yxt.modelhub.api.bean.dto.AmBatchDel4ReqDTO;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.calimeet.CaliMeetUserAppService;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserAddCmd;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserVO;
import com.yxt.talent.rv.application.calimeet.expt.CaliMeetUserDataExporter;
import com.yxt.talent.rv.application.common.CommonAppService;
import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import com.yxt.talent.rv.controller.common.viewobj.EntityIdsVO;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetUser4Add;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.RvCalibrationPerson4Get;
import com.yxt.talent.rv.controller.manage.meet.command.NewCaliMeetUserDelCmd;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericFileExportVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yxt.talent.rv.application.calimeet.CaliMeetUserAppService.DATA_PERM_RESULT;
import static com.yxt.talent.rv.application.xpd.result.XpdResultAppService.NAV_CODE_XPD;

@Validated
@RestController
@RequiredArgsConstructor
@Tag(name = "校准人员管理", description = "管理端-校准人员管理相关接口")
@RequestMapping("/mgr")
public class CaliMeetUserManageController {
    private final CaliMeetUserAppService caliMeetUserAppService;
    private final AuthService authService;
    private final CaliMeetUserDataExporter caliMeetUserDataExporter;
    private final CommonAppService commonAppService;

    @Parameters(value = {@Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
            @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "校准人员列表")
    @PostMapping(value = "/calimeet/user/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<RvCalibrationPerson4Get> pageList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        CaliMeetUserQuery query = convertCaliMeetUserQuery(search);
        String caliMeetId = query.getCaliMeetId();
        Validate.isNotBlank(caliMeetId, ExceptionKeys.CALI_MEET_ID_IS_BLANK);
        PagingList<CaliMeetUserVO> caliMeetUserVOPagingList = this.pageCaliMeetUsers(caliMeetId, query);
        return this.convertRvCalibrationPerson4Get(caliMeetId, caliMeetUserVOPagingList);
    }

    private PagingList<RvCalibrationPerson4Get> convertRvCalibrationPerson4Get(String caliMeetId,
            PagingList<CaliMeetUserVO> datas) {
        if (datas == null || datas.getDatas() == null) {
            return CommonUtil.emptyPagingList();
        }

        List<RvCalibrationPerson4Get> results = new ArrayList<>();
        for (CaliMeetUserVO caliMeetUserVO : datas.getDatas()) {
            RvCalibrationPerson4Get rvCalibrationPerson4Get = new RvCalibrationPerson4Get();
            rvCalibrationPerson4Get.setId(caliMeetUserVO.getId());
            rvCalibrationPerson4Get.setUid(caliMeetUserVO.getUserId());
            rvCalibrationPerson4Get.setName(caliMeetUserVO.getFullname());
            rvCalibrationPerson4Get.setCalibrationId(caliMeetId);
            //            rvCalibrationPerson4Get.setCalibrationId__Record(new Calibration4Get());
            //            rvCalibrationPerson4Get.setDeleted(0);
            rvCalibrationPerson4Get.setUserId(buildUserInfo(caliMeetUserVO));
            rvCalibrationPerson4Get.setCaliStatus(caliMeetUserVO.getCaliStatus());
            //            rvCalibrationPerson4Get.setStatus("");
            rvCalibrationPerson4Get.setRecordId(buildCaliRecord(caliMeetUserVO));
            rvCalibrationPerson4Get.set_spares(new HashMap<>(2));
            results.add(rvCalibrationPerson4Get);
        }
        return new PagingList<>(results, datas.getPaging());
    }

    private AmSlDrawer4RespDTO buildCaliRecord(CaliMeetUserVO obj) {
        AmSlDrawer4RespDTO result = new AmSlDrawer4RespDTO();
        List<Object> datas = Lists.newArrayList();
        result.setDatas(datas);

        Map<String, Object> calibrator = new HashMap<>(2);
        calibrator.put("name", obj.getCaliUserFullName()); // 校准人姓名

        Map<String, Object> userId = new HashMap<>(2);
        userId.put("status", obj.getCaliStatusStr());  // 校准状态

        Map<String, Object> data = new HashMap<>(2);
        data.put("@userId", userId);
        data.put("@calibrator", calibrator);
        data.put("calibTime", obj.getCaliTime());
        datas.add(data);

        return result;
    }

    private AmSlDrawer4RespDTO buildUserInfo(CaliMeetUserVO obj) {
        AmSlDrawer4RespDTO userId = new AmSlDrawer4RespDTO();
        List<Object> datas = Lists.newArrayList();
        userId.setDatas(datas);

        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(obj.getUserId());
        userInfo.setUserName(obj.getUsername());
        userInfo.setName(obj.getFullname());
        userInfo.setImgUrl(obj.getImgUrl());
        userInfo.setUserNo(obj.getUserNo());
        // 0-禁用 1-启用 2-删除
        userInfo.setStatus(String.valueOf(obj.getStatus()));

        AmUser4DTO.AmDept deptList = new AmUser4DTO.AmDept();
        List<AmUser4DTO.DeptInfo> deptInfoDatas = Lists.newArrayList();
        deptList.setDatas(deptInfoDatas);
        AmUser4DTO.DeptInfo deptInfo = new AmUser4DTO.DeptInfo();
        deptInfo.setId(obj.getDeptId());
        deptInfo.setName(obj.getDeptName());
        deptInfoDatas.add(deptInfo);

        userInfo.setDeptList(deptList);

        AmUser4DTO.AmPosition positionList = new AmUser4DTO.AmPosition();
        List<AmUser4DTO.PositionInfo> positionInfoDatas = Lists.newArrayList();
        positionList.setDatas(positionInfoDatas);
        AmUser4DTO.PositionInfo positionInfo = new AmUser4DTO.PositionInfo();
        positionInfo.setId(obj.getPositionId());
        positionInfo.setName(obj.getPositionName());
        positionInfoDatas.add(positionInfo);

        userInfo.setPositionList(positionList);

        AmUser4DTO.AmGrade gradeList = new AmUser4DTO.AmGrade();
        List<AmUser4DTO.GradeInfo> gradeInfoDatas = Lists.newArrayList();
        gradeList.setDatas(gradeInfoDatas);
        AmUser4DTO.GradeInfo gradeInfo = new AmUser4DTO.GradeInfo();
        gradeInfo.setName(obj.getGradeName());
        gradeInfoDatas.add(gradeInfo);

        userInfo.setGradeList(gradeList);

        datas.add(userInfo);

        return userId;
    }

    private CaliMeetUserQuery convertCaliMeetUserQuery(QueryUtil.Search search) {
        CaliMeetUserQuery query = new CaliMeetUserQuery();

        String dimCombId = search.getFilterEq().get("dimCombId");
        if (StringUtils.isNotBlank(dimCombId)) {
            query.setDimCombId(dimCombId);
        }

        String calimeetId = search.getFilterEq().get("calimeetId");
        if (StringUtils.isNotBlank(calimeetId)) {
            query.setCaliMeetId(calimeetId);
        }

        String caliStatusStr = search.getFilterEq().get("caliStatus");
        if (StringUtils.isNotBlank(caliStatusStr) && AomNumberUtils.isNumber(caliStatusStr)) {
            query.setCaliStatus(Integer.valueOf(caliStatusStr));
        }

        String positionId = search.getFilterEq().get("userId.positionId");
        if (StringUtils.isNotBlank(positionId)) {
            query.setPosIds(Lists.newArrayList(positionId));
        }

        String deptId = search.getFilterEq().get("userId.deptId");
        if (StringUtils.isNotBlank(deptId)) {
            query.setScopeDeptIds(Lists.newArrayList(deptId));
        }

        String gradeId = search.getFilterEq().get("userId.gradeId");
        if (StringUtils.isNotBlank(gradeId)) {
            query.setGradeIds(Lists.newArrayList(gradeId));
        }

        String searchKey = ApiUtil.getFiltedLikeString(search.getSearch().getValue());
        if (StringUtils.isNotBlank(searchKey)) {
            query.setSearchKey(searchKey);
            query.setKwType(CommonUtil.getKwType(search));
        }
        return query;
    }

    @Auth(type = {AuthType.TOKEN})
    @PostMapping("/calimeet/{caliMeetId}/user/list")
    @Operation(summary = "获取校准人员列表")
    public PagingList<CaliMeetUserVO> pageCaliMeetUsers(
            @PathVariable @Parameter(description = "校准会ID") String caliMeetId,
            @RequestBody CaliMeetUserQuery query) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        commonAppService.fillAuthInfo(userAuth.getOrgId(), query, NAV_CODE_XPD, DATA_PERM_RESULT);
        return caliMeetUserAppService.pageCaliMeetUsers(caliMeetId, query);
    }

    @Parameters(value = {
            @Parameter(name = "type", description = "1:导出全部 2：导出当前页 3：导出选中", in = ParameterIn.QUERY),
            @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
            @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "校准人员导出")
    @PostMapping(value = "/export", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public void export(@RequestBody SearchDTO bean, @RequestParam(defaultValue = "1") Integer type) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        CaliMeetUserQuery query = convertCaliMeetUserQuery(search);
        Validate.isNotBlank(query.getCaliMeetId(), ExceptionKeys.CALI_MEET_ID_IS_BLANK);
        this.export(query.getCaliMeetId(), query);
    }

    @Auth(type = {AuthType.TOKEN})
    @GetMapping("/calimeet/{caliMeetId}/user/export")
    @Operation(summary = "导出校准人员数据")
    @Auditing
    @EasyAuditLog(value  =RvAuditLogConstants.CALI_USER_EXPORT,paramExp = "#query.caliMeetId")
    public GenericFileExportVO export(@PathVariable @Parameter(description = "校准会ID") String caliMeetId,
            @RequestBody CaliMeetUserQuery query) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        commonAppService.fillAuthInfo(userAuth.getOrgId(), query, NAV_CODE_XPD, DATA_PERM_RESULT);
        return caliMeetUserDataExporter.export(caliMeetId, userAuth.getOrgId(), userAuth.getUserId(), query);
    }

    @Auth(type = {AuthType.TOKEN})
    @PostMapping("/calimeet/{caliMeetId}/user/not-added")
    @Operation(summary = "获取未添加的可校准人员列表")
    public PagingList<CaliMeetUser4Add> listNotAddedUsers(
        @PathVariable @Parameter(description = "校准会ID") String caliMeetId, @RequestBody SearchUdpScopeAuthQuery query) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        commonAppService.fillAuthInfo(userAuth.getOrgId(), query, NAV_CODE_XPD, DATA_PERM_RESULT);
        return caliMeetUserAppService.listNotAddedUsers(caliMeetId, query);
    }

    @Auth(type = {AuthType.TOKEN})
    @PostMapping("/calimeet/{caliMeetId}/user/add")
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "添加校准人员并保存盘点结果快照")
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.CALI_USER_ADD, paramExp = "{'meetId':#caliMeetId,'userIds':#addCmd.userIds}")
    public int addCaliMeetUsers(
            @PathVariable @Parameter(description = "校准会ID") String caliMeetId,
            @RequestBody @Validated CaliMeetUserAddCmd addCmd) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        return caliMeetUserAppService.addCaliMeetUsers(userAuth.getOrgId(), caliMeetId, addCmd);
    }


    @Parameter(name = "id", description = "校准人员id", in = ParameterIn.PATH)
    @Operation(summary = "校准人员删除（单个）")
    @DeleteMapping(value = "/calimeet/user/{id}")
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.CALI_USER_DEL, paramExp = "{'id': #id}")
    @Auth(action = Constants.LOG_TYPE_DELETESINGLE, type = {AuthType.TOKEN})
    public void delete(@PathVariable String id) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        caliMeetUserAppService.deleteCalimeetUser(id, userCacheBasic.getOrgId(), userCacheBasic.getUserId());
    }


    @PostMapping(value = "/calimeet/user/delete", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "删除校准人员（批量）")
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.CALI_USER_DEL, paramExp = "{'ids':#addCmd.userIds}")
    @Auth(action = Constants.LOG_TYPE_DELETESINGLE, type = {AuthType.TOKEN})
    public void deleteCaliMeetUsers(@RequestBody @Validated EntityIdsVO form) {
        UserCacheDetail userAuth = authService.getUserCacheDetail();
        caliMeetUserAppService.deleteCaliMeetUsers(form.getIds(), userAuth.getOrgId(), userAuth.getUserId());
    }

    @Operation(summary = "校准人员批量删除（APass）")
    @PostMapping(value = "/calimeet/user/batch_delete", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_DELETEMULTIPLE, type = {AuthType.TOKEN})
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.CALI_USER_DEL)
    public Object batchDelete(@RequestBody AmBatchDel4ReqDTO bean) {
        SearchDTO search = new SearchDTO();
        search.setSearch(bean.getSearch());
        search.setFilter(bean.getFilter());

        List<String> caliMeetIds;
        if (bean.getIsAll() != null && bean.getIsAll() == 1) {
            // 全选
            CaliMeetUserQuery query = convertCaliMeetUserQuery(QueryUtil.parse(search));
            String caliMeetId = query.getCaliMeetId();
            Validate.isNotBlank(caliMeetId, ExceptionKeys.CALI_MEET_ID_IS_BLANK);
            query.setOpenAuth(false);
            List<CaliMeetUserVO> caliMeetUsers = caliMeetUserAppService.listCaliMeetUsers(caliMeetId, query);
            caliMeetIds = caliMeetUsers.stream().map(CaliMeetUserVO::getId).toList();
        } else {
            // 勾选
            caliMeetIds = bean.getDatas();
        }
        NewCaliMeetUserDelCmd caliMeetUserDelCmd = new NewCaliMeetUserDelCmd();
        caliMeetUserDelCmd.setIds(caliMeetIds);
        AuditLogHooker.setLogParam(caliMeetUserDelCmd);
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        caliMeetUserAppService.deleteCaliMeetUsers(caliMeetIds, userCacheBasic.getOrgId(), userCacheBasic.getUserId());
        return Maps.newHashMap();
    }

}
