package com.yxt.talent.rv.controller.manage.calimeet.query;

import com.yxt.talent.rv.controller.common.query.SearchKeyScopeAuthQuery;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

/**
 * 校准幅度较大人员查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "校准幅度较大人员查询参数")
public class CaliMeetLargeChangePersonnelQry extends SearchKeyScopeAuthQuery {
    
    /**
     * 维度组合Key
     */
    @NotBlank(message = ExceptionKeys.CALI_DIM_COMB_ID_NOT_BLANK)
    @Schema(description = "维度组合id", requiredMode = REQUIRED)
    private String dimCombId;
} 